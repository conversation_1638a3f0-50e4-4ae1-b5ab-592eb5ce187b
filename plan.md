################################################################################################################
Readme.md :- 


# Shopify OAuth Integration & Token Exchange

This document explains how this FastAPI application implements Shopify OAuth authentication to connect and authorize access to Shopify stores, including MySQL database implementation for storing tokens and store data.

## Overview

The application implements a complete Shopify OAuth 2.0 flow that allows Shopify store owners to install and authorize the app to access their store data. The process involves:

1. **Installation Flow** - Redirecting users to Shopify's authorization page
2. **OAuth Callback** - Handling the authorization response from Shopify
3. **Token Exchange** - Converting the authorization code into a permanent access token
4. **Store Data Fetching** - Using the token to fetch store information via GraphQL
5. **Secure Storage** - Storing tokens and store data securely in MySQL database

## Requirements

- Python 3.8+
- MySQL Database
- Shopify Partner Account with API credentials

## Required Environment Variables

```env
# Shopify OAuth Configuration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SHOPIFY_SCOPES=read_products,write_products,read_customers,write_customers,read_orders
SHOPIFY_API_VERSION=2025-01
SHOPIFY_REDIRECT_URI=https://yourdomain.com/app/callback
SHOPIFY_GRAPHQL_URI=https://{shop}/admin/api/{version}/graphql.json

# Database Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost/database_name
```

## MySQL Database Implementation

### Database Schema

The application uses a `stores` table to store Shopify store information and access tokens:

<augment_code_snippet path="app/db/models/store.py" mode="EXCERPT">
````python
class Store(Base):
    __tablename__ = "stores"

    id = Column(BigInteger, primary_key=True, index=True)
    shopify_store_id = Column(String(255), unique=True, nullable=False)
    guid = Column(String(36), unique=True, nullable=False)
    token = Column(String(255), nullable=False)  # Base64 encoded access token
    is_deleted = Column(Boolean, default=False)

    # Store Information
    name = Column(String(255))
    email = Column(String(255))
    domain = Column(String(255))
    myshopify_domain = Column(String(255))

    # Location Data
    city = Column(String(100))
    country = Column(String(56))
    country_code = Column(String(4))
    latitude = Column(Double)
    longitude = Column(Double)

    # Store Settings
    currency = Column(String(4))
    money_format = Column(String(50))
    iana_timezone = Column(String(100))
    phone = Column(String(20))
````
</augment_code_snippet>

### Database Connection Setup

<augment_code_snippet path="app/db/session.py" mode="EXCERPT">
````python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

# Create the SQLAlchemy engine with MySQL connection
engine = create_engine(settings.DATABASE_URL, pool_pre_ping=True)

# Configure the session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_db():
    """Initialize database tables"""
    from app.db.base import Base
    Base.metadata.create_all(bind=engine)
````
</augment_code_snippet>

## Shopify OAuth Flow Step-by-Step

### Step 1: Installation Endpoint (`/app/install`)

The installation process begins when a user visits the install endpoint with their shop domain.

<augment_code_snippet path="app/api/v1/auth.py" mode="EXCERPT">
````python
@router.get("/install")
def install(shop: str):
    """
    Step 1: Initiate Shopify OAuth process by redirecting to Shopify authorization page
    """
    if not shop.endswith(".myshopify.com"):
        logger.warning(f"Invalid shop domain attempted: {shop}")
        raise HTTPException(status_code=400, detail="Invalid Shopify domain")

    oauth_url = shopify_service.generate_oauth_url(shop)
    logger.info(f"Redirecting shop {shop} to Shopify OAuth: {oauth_url}")

    return RedirectResponse(url=oauth_url)
````
</augment_code_snippet>

**What happens:**
- Validates the shop domain format (must end with `.myshopify.com`)
- Generates the OAuth authorization URL
- Redirects the user to Shopify's authorization page

### Step 2: OAuth URL Generation

<augment_code_snippet path="app/services/shopify_service.py" mode="EXCERPT">
````python
def generate_oauth_url(shop: str) -> str:
    return f"https://{shop}/admin/oauth/authorize?client_id={settings.SHOPIFY_API_KEY}&scope={settings.SHOPIFY_SCOPES}&redirect_uri={settings.SHOPIFY_REDIRECT_URI}"
````
</augment_code_snippet>

**URL Structure:**
```
https://shop-name.myshopify.com/admin/oauth/authorize?
  client_id=YOUR_API_KEY&
  scope=read_products,write_products,read_customers&
  redirect_uri=https://yourdomain.com/app/callback
```

### Step 3: OAuth Callback Handler (`/app/callback`)

After the user authorizes the app, Shopify redirects back to this endpoint with an authorization code.

<augment_code_snippet path="app/api/v1/auth.py" mode="EXCERPT">
````python
@router.get("/callback")
def oauth_callback(request: Request, db: Session = Depends(get_db)):
    """
    Shopify OAuth Callback:
    - Validates HMAC
    - Exchanges code for access token
    - Fetches shop data via Shopify GraphQL
    - Saves/updates store info in DB
    """
    try:
        params = dict(request.query_params)

        # Validate required params
        required = ["hmac", "shop", "code"]
        for r in required:
            if r not in params:
                raise HTTPException(status_code=400, detail=f"Missing param: {r}")

        # Verify HMAC for security
        if not shopify_utils.verify_hmac(params, params["hmac"]):
            raise HTTPException(status_code=401, detail="HMAC verification failed")

        shop = params["shop"]
        code = params["code"]

        # Exchange code for permanent access token
        token = shopify_service.exchange_code_for_token(shop, code)

        # Fetch shop data from GraphQL
        shop_data = shopify_service.fetch_shop_data_from_graphql(shop, token)

        # Save or update store in DB
        store = shopify_service.save_or_update_store(db, shop_data, token)
````
</augment_code_snippet>

### Step 4: Token Exchange Process

The most critical part - converting the temporary authorization code into a permanent access token.

<augment_code_snippet path="app/services/shopify_service.py" mode="EXCERPT">
````python
def exchange_code_for_token(shop: str, code: str) -> str:
    """Exchange OAuth code for permanent access token"""
    url = f"https://{shop}/admin/oauth/access_token"
    payload = {
        "client_id": settings.SHOPIFY_API_KEY,
        "client_secret": settings.SHOPIFY_API_SECRET,
        "code": code,
    }
    response = requests.post(url, json=payload, verify=False)
    response.raise_for_status()
    access_token = response.json()["access_token"]
    logger.info(f"Shopify token received for shop {shop}")
    return access_token
````
</augment_code_snippet>

**Token Exchange Request:**
```json
POST https://shop-name.myshopify.com/admin/oauth/access_token
{
  "client_id": "your_api_key",
  "client_secret": "your_api_secret",
  "code": "authorization_code_from_callback"
}
```

**Response:**
```json
{
  "access_token": "shpat_xxxxxxxxxxxxxxxxxxxxx",
  "scope": "read_products,write_products"
}
```

### Step 5: Fetching Store Data with Token

Once we have the access token, we fetch store information using Shopify's GraphQL API.

<augment_code_snippet path="app/services/shopify_service.py" mode="EXCERPT">
````python
def fetch_shop_data_from_graphql(shop: str, token: str) -> dict:
    """Fetch shop information using Shopify GraphQL API"""
    url = settings.SHOPIFY_GRAPHQL_URI.format(shop=shop, version=settings.SHOPIFY_API_VERSION)
    headers = {
        "X-Shopify-Access-Token": token,
        "Content-Type": "application/json"
    }
    query = {
        "query": """
        {
          shop {
            id
            name
            email
            myshopifyDomain
            shopOwnerName
            primaryDomain { host }
            currencyCode
            billingAddress {
              phone
              city
              country
              countryCodeV2
              province
              latitude
              longitude
              zip
            }
          }
        }
        """
    }
    response = requests.post(url, json=query, headers=headers)
    return response.json()
````
</augment_code_snippet>

### Step 6: Storing Data in MySQL Database

After fetching store data, the application saves or updates the store information in the MySQL database.

<augment_code_snippet path="app/services/shopify_service.py" mode="EXCERPT">
````python
def save_or_update_store(db: Session, shop_data: dict, token: str) -> Store:
    """Save or update store information in database"""
    shop_info = shop_data["data"]["shop"]
    shopify_store_id = shop_info["id"].split("/")[-1]

    # Check if store already exists
    existing_store = db.query(Store).filter(
        Store.shopify_store_id == shopify_store_id
    ).first()

    if existing_store:
        # Update existing store
        existing_store.token = encode_token(token)
        existing_store.name = shop_info.get("name")
        existing_store.email = shop_info.get("email")
        existing_store.domain = shop_info.get("primaryDomain", {}).get("host")
        existing_store.myshopify_domain = shop_info.get("myshopifyDomain")
        store = existing_store
    else:
        # Create new store
        store = Store(
            shopify_store_id=shopify_store_id,
            guid=str(uuid.uuid4()),
            token=encode_token(token),
            name=shop_info.get("name"),
            email=shop_info.get("email"),
            domain=shop_info.get("primaryDomain", {}).get("host"),
            myshopify_domain=shop_info.get("myshopifyDomain")
        )
        db.add(store)

    db.commit()
    db.refresh(store)
    return store
````
</augment_code_snippet>

## Security Implementation

### HMAC Verification

Every callback from Shopify includes an HMAC signature to verify authenticity.

<augment_code_snippet path="app/utils/shopify_utils.py" mode="EXCERPT">
````python
def verify_hmac(params: dict, hmac_header: str) -> bool:
    """Verify Shopify HMAC to secure callback"""
    # Remove hmac from params for verification
    params = {k: v for k, v in params.items() if k != 'hmac'}
    message = urlencode(sorted(params.items()))

    secret = settings.SHOPIFY_API_SECRET.encode('utf-8')
    hash_ = hmac.new(secret, message.encode('utf-8'), hashlib.sha256).hexdigest()
    is_valid = hmac.compare_digest(hash_, hmac_header)

    return is_valid
````
</augment_code_snippet>

### Token Storage Security

Tokens are Base64 encoded before storage for additional security.

<augment_code_snippet path="app/utils/token_base64.py" mode="EXCERPT">
````python
def encode_token(token: str) -> str:
    return base64.urlsafe_b64encode(token.encode()).decode()

def decode_token(encoded_token: str) -> str:
    return base64.urlsafe_b64decode(encoded_token.encode()).decode()
````
</augment_code_snippet>

## What Gets Stored in MySQL Database

The application stores the following information for each connected Shopify store:

### Core Store Data
- **shopify_store_id**: Unique Shopify store identifier
- **guid**: Application-generated unique identifier
- **token**: Base64 encoded access token for API calls
- **name**: Store name
- **email**: Store owner email
- **domain**: Primary domain (e.g., mystore.com)
- **myshopify_domain**: Shopify subdomain (e.g., mystore.myshopify.com)

### Location Information
- **city**: Store location city
- **country**: Store country
- **country_code**: ISO country code
- **latitude/longitude**: Geographic coordinates
- **phone**: Store contact phone

### Store Settings
- **currency**: Store currency code
- **money_format**: Currency display format
- **iana_timezone**: Store timezone
- **is_deleted**: Soft delete flag

## OAuth Flow Summary

1. **User visits**: `/app/install?shop=store-name.myshopify.com`
2. **App redirects to**: `https://store-name.myshopify.com/admin/oauth/authorize?client_id=...`
3. **User authorizes** the app on Shopify
4. **Shopify redirects to**: `/app/callback?code=auth_code&shop=store-name.myshopify.com&hmac=...`
5. **App validates** HMAC signature
6. **App exchanges** authorization code for permanent access token
7. **App fetches** store data using the token via GraphQL
8. **App stores** token and store information securely in MySQL database

## Key OAuth Endpoints

- **Installation**: `GET /app/install?shop={shop_domain}`
- **Callback**: `GET /app/callback` (handles Shopify's response)

This implementation provides a secure, complete OAuth flow for connecting to any Shopify store and obtaining the necessary permissions to access store data, with all information securely stored in a MySQL database for future API calls.





################################################################################################################



Requirements.txt

chromadb
longchain
fastapi[All]
uvicorn
sqlalchemy
pymysql
pydantic
pydantic-settings
python-dotenv
requests
cryptography
boto3
langchain-community
langchain-openai


################################################################################################################

.env 


SHOPIFY_API_KEY = 
SHOPIFY_API_SECRET = 
SHOPIFY_SCOPES=read_products,write_products,read_customers,write_customers,read_orders,read_all_orders,write_orders,read_draft_orders,write_draft_orders,read_fulfillments,write_fulfillments,read_inventory,write_inventory,read_locations
SHOPIFY_API_VERSION = 2025-01
SHOPIFY_REDIRECT_URI = https://exampledomain.com/app/callback
SHOPIFY_GRAPHQL_URI = https://{shop}/admin/api/{version}/graphql.json


################################################################################################################


main.py 



from fastapi import FastAPI
from app.core.logger import setup_logger
from app.db.session import init_db
from app.api.v1 import auth, sync_data, query
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi import Request, HTTPException
from app.utils.response_formatter import error_response
from starlette.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pathlib import Path
from fastapi.staticfiles import StaticFiles

# Initialize the global logger
logger = setup_logger()
 
# Create the FastAPI app instance
app = FastAPI(title="Shopify Assistant", version="1.0")
 
# Allow all origins
app.add_middleware(
    CORSMiddleware,
    allow_headers=["*"],  # Allow all headers
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
)
 
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    logger.error(f"[HTTPException] {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(msg=str(exc.detail))
    )
 
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(f"[ValidationError] {exc}")
    return JSONResponse(
        status_code=422,
        content=error_response(
            msg="Validation failed",
            data=exc.errors()
        )
    )
 
@app.exception_handler(Exception)
async def unhandled_exception_handler(request: Request, exc: Exception):
    logger.exception(f"[UnhandledException] {exc}")
    return JSONResponse(
        status_code=500,
        content=error_response(msg=str(exc))
    )
 
 
# Initialize the database
init_db()
 
# Include routers
app.include_router(auth.router, prefix="/app", tags=["Auth"])
app.include_router(sync_data.router, prefix="/api/v1", tags=["SyncData"])
app.include_router(query.router, prefix="/api/v1", tags=["AI"])

# Serve static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def serve_frontend():
    """
    Serve the index.html file for the frontend.
    """
    index_file = Path("app/static/index.html")
    if index_file.exists():
        return HTMLResponse(content=index_file.read_text(), status_code=200)
    return HTMLResponse(content="Frontend not found", status_code=404)



################################################################################################################



tokenbase64.py


import base64

def encode_token(token: str) -> str:
    return base64.urlsafe_b64encode(token.encode()).decode()

def decode_token(encoded_token: str) -> str:
    return base64.urlsafe_b64decode(encoded_token.encode()).decode()

################################################################################################################

shopify_utils.py


import hmac
import hashlib
import base64
import json
import time
from urllib.parse import urlencode
from fastapi import HTTPException
from app.core.config import settings
from app.core.logger import setup_logger

# Initialize the logger
logger = setup_logger()

def verify_hmac(params: dict, hmac_header: str) -> bool:
    """Verify Shopify HMAC to secure callback"""
    try:
        logger.info("Starting HMAC verification.")
        params = {k: v for k, v in params.items() if k != 'hmac'}
        message = urlencode(sorted(params.items()))
        logger.debug(f"Message for HMAC: {message}")
        
        secret = settings.SHOPIFY_API_SECRET.encode('utf-8')
        hash_ = hmac.new(secret, message.encode('utf-8'), hashlib.sha256).hexdigest()
        is_valid = hmac.compare_digest(hash_, hmac_header)
        
        if is_valid:
            logger.info("HMAC verification successful.")
        else:
            logger.warning("HMAC verification failed.")
        
        return is_valid
    except Exception as e:
        logger.exception(f"Error during HMAC verification: {e}")
        raise HTTPException(status_code=500, detail="HMAC verification failed")

def verify_shopify_session_token(token: str) -> dict:
    """
    Verifies Shopify session token using HMAC SHA256
    Returns the decoded payload if valid
    """
    try:
        logger.info("Starting Shopify session token verification.")
        header_b64, payload_b64, signature_b64 = token.split(".")
        logger.info(f"Token parts - Header: {header_b64}, Payload: {payload_b64}, Signature: {signature_b64}")

        signature_check = hmac.new(
            key=settings.SHOPIFY_API_SECRET.encode(),
            msg=f"{header_b64}.{payload_b64}".encode(),
            digestmod=hashlib.sha256
        ).digest()

        actual_signature = base64.urlsafe_b64decode(signature_b64 + "==")
        if not hmac.compare_digest(signature_check, actual_signature):
            logger.warning("Shopify session signature verification failed.")
            raise HTTPException(status_code=401, detail="Invalid Shopify session signature")

        payload_json = base64.urlsafe_b64decode(payload_b64 + "==").decode()
        payload = json.loads(payload_json)
        logger.debug(f"Decoded payload: {payload}")

        # Expiration check
        if payload.get("exp") < int(time.time()):
            logger.warning("Shopify session token has expired.")
            raise HTTPException(status_code=401, detail="Shopify session expired")

        logger.info("Shopify session token verification successful.")
        return payload

    except Exception as e:
        logger.exception(f"Error during Shopify session token verification: {e}")
        raise HTTPException(status_code=401, detail=f"Invalid Shopify session token: {str(e)}")


################################################################################################################

shopify_service.py


import requests
import uuid
from sqlalchemy.orm import Session
from app.db.models.store import Store
from app.core.config import settings
from app.utils.token_base64 import encode_token, decode_token
from app.core.logger import setup_logger

# Initialize the global logger
logger = setup_logger()

def generate_oauth_url(shop: str) -> str:
    return f"https://{shop}/admin/oauth/authorize?client_id={settings.SHOPIFY_API_KEY}&scope={settings.SHOPIFY_SCOPES}&redirect_uri={settings.SHOPIFY_REDIRECT_URI}"

def exchange_code_for_token(shop: str, code: str) -> str:
    """Exchange OAuth code for permanent access token"""
    url = f"https://{shop}/admin/oauth/access_token"
    payload = {
        "client_id": settings.SHOPIFY_API_KEY,
        "client_secret": settings.SHOPIFY_API_SECRET,
        "code": code,
    }
    response = requests.post(url, json=payload, verify=False)
    response.raise_for_status()
    access_token = response.json()["access_token"]
    logger.info(f"Shopify token received for shop {shop}:{access_token}")
    return access_token

def fetch_shop_data_from_graphql(shop: str, token: str) -> dict:
    """Fetch shop information using Shopify GraphQL API"""
    url = settings.SHOPIFY_GRAPHQL_URI.format(shop=shop, version=settings.SHOPIFY_API_VERSION)
    headers = {
        "X-Shopify-Access-Token": token,
        "Content-Type": "application/json"
    }
    query = {
        "query": """
        {
          shop {
            id
            name
            email
            myshopifyDomain
            shopOwnerName
            plan {
              displayName
            }
            # customers(first: 250) {
            #     edges {
            #         node {
            #             email
            #         }
            #     }
            # }
            primaryDomain {
              host
            }
            currencyFormats{
                moneyFormat
            }
            billingAddress {
              phone
              city
              country
              countryCodeV2
              province
              provinceCode
              latitude
              longitude
              zip
            }
            currencyCode
            ianaTimezone
            weightUnit
            timezoneAbbreviation
            
          }
        }
        """
    }

    try:
        logger.info(f"Sending GraphQL request to Shopify for shop: {shop}")
        response = requests.post(url, json=query, headers=headers, verify=False)
        response.raise_for_status()  # Raise an HTTPError for bad responses (4xx and 5xx)
        logger.info(f"GraphQL request successful for shop: {shop} Response: {response.json()}")

        shop_data = response.json().get("data", {}).get("shop", {})
        if not shop_data:
            logger.error(f"No shop data found in the response for shop: {shop}")
            raise Exception("Failed to fetch shop info: No data returned from Shopify API")

        logger.info(f"Shop data successfully fetched for shop: {shop}")
        return shop_data

    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP request error while fetching shop data for shop {shop}: {e}")
        raise Exception(f"HTTP request error: {e}")

    except Exception as e:
        logger.error(f"Unexpected error while fetching shop data for shop {shop}: {e}")
        raise Exception(f"Unexpected error: {e}")

def save_or_update_store(db: Session, shop_data: dict, token: str) -> Store:
    """
    Save or update the Shopify store data in the database.

    Args:
        db (Session): SQLAlchemy database session.
        shop_data (dict): Shopify shop data fetched from the API.
        token (str): Shopify access token.

    Returns:
        Store: The saved or updated store instance.

    Raises:
        Exception: If there is an error during the database operation.
    """
    myshopify_domain = shop_data.get("myshopifyDomain")
    if not myshopify_domain:
        logger.error("Shop data is missing 'myshopifyDomain'. Cannot save or update store.")
        raise Exception("Invalid shop data: 'myshopifyDomain' is required.")

    try:
        logger.info(f"Checking for existing store record for {myshopify_domain}")
        existing_store = db.query(Store).filter_by(myshopify_domain=myshopify_domain).first()

        encoded_token = encode_token(token)

        if not existing_store:
            logger.info(f"Creating new store record for {myshopify_domain}")
            store = Store(
                shopify_store_id=shop_data.get("id"),  # Shopify store ID
                guid=str(uuid.uuid4()),  # Unique identifier for the store
                token=encoded_token,  # Encoded Shopify access token
                myshopify_domain=myshopify_domain,  # Shopify domain
                name=shop_data.get("name"),  # Store name
                email=shop_data.get("email"),  # Store email
                domain=shop_data.get("primaryDomain", {}).get("host"),  # Primary domain
                money_format=shop_data.get("currencyFormats", {}).get("moneyFormat"),  # Money format
                shop_owner=shop_data.get("shopOwnerName"),  # Shop owner's name
                phone=shop_data.get("billingAddress", {}).get("phone"),  # Billing phone
                city=shop_data.get("billingAddress", {}).get("city"),  # Billing city
                country=shop_data.get("billingAddress", {}).get("country"),  # Billing country
                country_name=shop_data.get("billingAddress", {}).get("country"),
                country_code=shop_data.get("billingAddress", {}).get("countryCodeV2"),  # Country code
                province=shop_data.get("billingAddress", {}).get("province"),  # Billing province
                province_code=shop_data.get("billingAddress", {}).get("provinceCode"),  # Province code
                zip=shop_data.get("billingAddress", {}).get("zip"),  # Billing ZIP code
                currency=shop_data.get("currencyCode"),  # Store currency
                iana_timezone=shop_data.get("ianaTimezone"),  # IANA timezone
                timezone=shop_data.get("timezoneAbbreviation"),  # timezone
                latitude=shop_data.get("billingAddress", {}).get("latitude"),  # Store latitude
                longitude=shop_data.get("billingAddress", {}).get("longitude"),  # Store longitude
                weight_unit=shop_data.get("weightUnit"),  # Weight unit
            )
            db.add(store)
        else:
            logger.info(f"Updating existing store record for {myshopify_domain}")
            existing_store.token = encoded_token
            existing_store.name = shop_data.get("name")
            existing_store.email = shop_data.get("email")
            existing_store.domain = shop_data.get("primaryDomain", {}).get("host")
            existing_store.money_format = shop_data.get("currencyFormats", {}).get("moneyFormat")
            existing_store.shop_owner = shop_data.get("shopOwnerName")
            existing_store.phone = shop_data.get("billingAddress", {}).get("phone")
            existing_store.city = shop_data.get("billingAddress", {}).get("city")
            existing_store.country = shop_data.get("billingAddress", {}).get("country")
            existing_store.country_name = shop_data.get("billingAddress", {}).get("country")
            existing_store.country_code = shop_data.get("billingAddress", {}).get("countryCodeV2")
            existing_store.province = shop_data.get("billingAddress", {}).get("province")
            existing_store.province_code = shop_data.get("billingAddress", {}).get("provinceCode")
            existing_store.zip = shop_data.get("billingAddress", {}).get("zip")
            existing_store.currency = shop_data.get("currencyCode")
            existing_store.iana_timezone = shop_data.get("ianaTimezone")
            existing_store.timezone = shop_data.get("timezoneAbbreviation")
            existing_store.latitude = shop_data.get("latitude")
            existing_store.longitude = shop_data.get("longitude")
            existing_store.weight_unit = shop_data.get("weightUnit")

            store = existing_store

        db.commit()
        db.refresh(store)
        logger.info(f"Store record saved successfully. Store GUID: {store.guid}")
        return store

    except Exception as e:
        logger.error(f"Error while saving or updating store for {myshopify_domain}: {e}")
        db.rollback()
        raise Exception(f"Failed to save or update store: {e}")

def get_store_by_domain(db: Session, domain: str) -> Store:
    """
    Fetches a store from the database using the myshopify domain.
    This is used after validating a Shopify session token.

    :param db: SQLAlchemy DB session
    :param domain: Shop domain like 'example.myshopify.com'
    :return: Store object or None
    """
    store = db.query(Store).filter_by(myshopify_domain=domain, is_deleted=False).first()
    logger.info(f"Get Store By Domain : {store}")
    return store

def get_decoded_token(store: Store) -> str:
    """Decode the stored Base64 token before use"""
    return decode_token(store.token)


############################################################################################################

api.py


from fastapi import APIRouter, Request, Depends, HTTPException
from sqlalchemy.orm import Session
from starlette.responses import RedirectResponse
from app.services import shopify_service
from app.utils import shopify_utils
from app.db.session import SessionLocal
from app.core.logger import setup_logger

# Initialize the global logger
logger = setup_logger()

router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/install")
def install(shop: str):
    """
    Step 1: Initiate Shopify OAuth process by redirecting to Shopify authorization page
    """
    if not shop.endswith(".myshopify.com"):
        logger.warning(f"Invalid shop domain attempted: {shop}")
        raise HTTPException(status_code=400, detail="Invalid Shopify domain")

    oauth_url = shopify_service.generate_oauth_url(shop)
    logger.info(f"Redirecting shop {shop} to Shopify OAuth: {oauth_url}")

    return RedirectResponse(url=oauth_url)

@router.get("/callback")
def oauth_callback(request: Request, db: Session = Depends(get_db)):
    """
    Shopify OAuth Callback:
    - Validates HMAC
    - Exchanges code for access token
    - Fetches shop data via Shopify GraphQL
    - Saves/updates store info in DB
    - Redirects to frontend studio with store GUID
    """
    try:
        params = dict(request.query_params)
        logger.info("OAuth callback received.")

        # Validate required params
        required = ["hmac", "shop", "code"]
        for r in required:
            if r not in params:
                logger.error(f"Missing required parameter: {r}")
                raise HTTPException(status_code=400, detail=f"Missing param: {r}")

        # HMAC Verification
        is_valid = shopify_utils.verify_hmac(params, params["hmac"])
        if not is_valid:
            logger.error("HMAC validation failed")
            raise HTTPException(status_code=400, detail="Invalid HMAC signature")

        shop = params["shop"]
        code = params["code"]
        host = params["host"]

        logger.info(f"Shop verified: {shop}. Exchanging code for token.")

        # Exchange code for token
        token = shopify_service.exchange_code_for_token(shop, code)

        # Fetch shop data from GraphQL
        shop_data = shopify_service.fetch_shop_data_from_graphql(shop, token)

        # Save or update store in DB
        store = shopify_service.save_or_update_store(db, shop_data, token)

        # Redirect to frontend
        redirect_url = f"https://shopassistant.lumezai.com?host={host}&shop={shop}"
        logger.info(f"Redirecting to studio: {redirect_url}")
        return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.exception(f"OAuth callback failed:{e}")
        raise HTTPException(status_code=500, detail=str(e))

############################################################################################################


